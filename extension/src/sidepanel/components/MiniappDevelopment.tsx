import React from 'react';
import { MiniApp } from '../../types/miniapp';
import { Tooltip } from 'antd';
import { X } from 'lucide-react';

import { UserInfo } from '~/storages/indexdb';

interface MiniappDevelopmentProps {
  miniapp: MiniApp;
  setCurrentMiniapp: (miniapp: MiniApp | null) => void;
  user: UserInfo | null;
}

const MiniappDevelopment = ({ miniapp, setCurrentMiniapp, user }: MiniappDevelopmentProps) => {
  const handleBack = () => {
    setCurrentMiniapp(null);
  };

  return (
    <div style={{ position: 'fixed', inset: 0, zIndex: 50 }}>
      {/* MiniApp Development Container */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: '#ffffff',
          boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
          overflow: 'hidden',
          zIndex: 10,
        }}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {miniapp.name} Development
          </h2>
          <Tooltip title="Close" placement="bottom">
            <button
              onClick={handleBack}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* TODO Messages Area */}
        <div
          style={{
            flex: 1,
            overflowY: 'auto',
            padding: '16px',
            height: 'calc(100vh - 44px)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '16px',
          }}
        >
          <div
            style={{
              textAlign: 'center',
              maxWidth: '400px',
            }}
          >
            <h3
              style={{
                fontSize: '24px',
                fontWeight: 600,
                color: '#111827',
                marginBottom: '8px',
              }}
            >
              Hello,
            </h3>
            <h4
              style={{
                fontSize: '20px',
                fontWeight: 500,
                color: '#111827',
                marginBottom: '16px',
              }}
            >
              I'm the MiniApp Assistant
            </h4>
            <p
              style={{
                fontSize: '16px',
                color: '#6b7280',
                lineHeight: 1.5,
              }}
            >
              Tell me what script you want to create
            </p>
          </div>
        </div>

        {/* TODO Input Area */}
      </div>
    </div>
  );
};

export default MiniappDevelopment;
