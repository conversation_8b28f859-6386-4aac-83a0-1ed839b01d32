import React, { useState } from 'react';
import { MiniApp } from '../../types/miniapp';
import { Tooltip, Modal, Input } from 'antd';
import { X, FolderPlus, Sparkles } from 'lucide-react';

import { useLiveQuery } from 'dexie-react-hooks';
import { db, UserInfo } from '~/storages/indexdb';
import MiniappEmptyState from './MiniappEmptyState';
import MiniappApplicationsList from './MiniappApplicationsList';

interface MiniappIndexProps {
  setShowMiniapp: (show: boolean) => void;
  setCurrentMiniapp: (miniapp: MiniApp | null) => void;
  user: UserInfo | null;
}

const MiniappIndex = ({ setShowMiniapp, setCurrentMiniapp, user }: MiniappIndexProps) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [projectName, setProjectName] = useState('');

  // Get all miniapps for the current user
  const miniapps = useLiveQuery(async () => {
    if (!user?.id) return [];
    const allApps = await db.getAllApps('all');
    return allApps.filter(app => app.userId === user.id);
  }, [user?.id]);

  const handleNewProject = () => {
    setShowCreateModal(true);
  };

  const handleCreateProject = async () => {
    if (!projectName.trim() || !user?.id) return;

    try {
      const now = new Date().toISOString();
      const newMiniapp: MiniApp = {
        id: Date.now(), // Simple ID generation
        userId: user.id,
        name: projectName.trim(),
        lastSelectedAt: now,
        createdAt: now,
        history: [],
      };

      await db.saveApplication(newMiniapp);

      // Navigate to development interface
      setCurrentMiniapp(newMiniapp);
      setShowMiniapp(false);

      // Reset modal state
      setShowCreateModal(false);
      setProjectName('');
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  const handleCancelCreate = () => {
    setShowCreateModal(false);
    setProjectName('');
  };

  const handleSelectMiniapp = (miniapp: MiniApp) => {
    setCurrentMiniapp(miniapp);
    setShowMiniapp(false);
  };

  const handleArchiveMiniapp = async (miniapp: MiniApp) => {
    // TODO: Implement archive functionality
    console.log('Archive miniapp:', miniapp.name);
  };

  const handleDeleteMiniapp = async (miniapp: MiniApp) => {
    // TODO: Implement delete functionality
    console.log('Delete miniapp:', miniapp.name);
  };

  return (
    <div style={{ position: 'fixed', inset: 0, zIndex: 50 }}>
      {/* Miniapp Index Container */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: '#ffffff',
          boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
          overflow: 'hidden',
          zIndex: 10,
        }}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>Applications</h2>
          <Tooltip title="Close" placement="bottom">
            <button
              onClick={() => setShowMiniapp(false)}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* Content */}
        <div
          style={{
            flex: 1,
            height: 'calc(100vh - 44px)',
          }}
        >
          {miniapps && miniapps.length > 0 ? (
            <MiniappApplicationsList
              miniapps={miniapps}
              onSelectMiniapp={handleSelectMiniapp}
              onNewProject={handleNewProject}
              onArchiveMiniapp={handleArchiveMiniapp}
              onDeleteMiniapp={handleDeleteMiniapp}
            />
          ) : (
            <MiniappEmptyState onNewProject={handleNewProject} />
          )}
        </div>
      </div>

      {/* Create Project Modal */}
      <Modal
        open={showCreateModal}
        onCancel={handleCancelCreate}
        footer={null}
        centered
        width={400}
        styles={{
          body: { padding: '32px 24px 24px' },
        }}
      >
        <div style={{ textAlign: 'center' }}>
          {/* Icon with sparkles */}
          <div
            style={{
              position: 'relative',
              display: 'inline-block',
              marginBottom: '24px',
            }}
          >
            <FolderPlus size={64} color="#374151" strokeWidth={1.5} />
            <Sparkles
              size={20}
              color="#fbbf24"
              style={{
                position: 'absolute',
                top: '-8px',
                right: '-8px',
              }}
            />
            <Sparkles
              size={16}
              color="#fbbf24"
              style={{
                position: 'absolute',
                bottom: '8px',
                left: '-12px',
              }}
            />
            <Sparkles
              size={12}
              color="#fbbf24"
              style={{
                position: 'absolute',
                top: '12px',
                left: '-8px',
              }}
            />
          </div>

          {/* Title */}
          <h2
            style={{
              fontSize: '24px',
              fontWeight: 600,
              color: '#111827',
              marginBottom: '32px',
              margin: '0 0 32px 0',
            }}
          >
            Create Project
          </h2>

          {/* Form */}
          <div style={{ textAlign: 'left', marginBottom: '32px' }}>
            <label
              style={{
                display: 'block',
                fontSize: '16px',
                fontWeight: 500,
                color: '#374151',
                marginBottom: '8px',
              }}
            >
              Project name
            </label>
            <Input
              placeholder="Please enter..."
              value={projectName}
              onChange={e => setProjectName(e.target.value)}
              onPressEnter={handleCreateProject}
              style={{
                height: '48px',
                fontSize: '16px',
                borderRadius: '8px',
              }}
              autoFocus
            />
          </div>

          {/* Buttons */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <button
              onClick={handleCreateProject}
              disabled={!projectName.trim()}
              style={{
                width: '100%',
                height: '48px',
                borderRadius: '8px',
                border: 'none',
                backgroundColor: projectName.trim() ? '#374151' : '#d1d5db',
                color: '#ffffff',
                fontSize: '16px',
                fontWeight: 500,
                cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                transition: 'background-color 0.2s',
              }}
            >
              Create
            </button>
            <button
              onClick={handleCancelCreate}
              style={{
                width: '100%',
                height: '48px',
                borderRadius: '8px',
                border: '1px solid #d1d5db',
                backgroundColor: '#ffffff',
                color: '#374151',
                fontSize: '16px',
                fontWeight: 500,
                cursor: 'pointer',
                transition: 'all 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#f9fafb';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = '#ffffff';
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MiniappIndex;
